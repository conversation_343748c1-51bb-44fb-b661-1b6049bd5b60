<?php 
$page_title = 'Bangladesh Unbound - Create Package';
$page_header = 'Create New Package';
include 'inc/header-admin.php'; 
?>

            <!-- Breadcrumb Navigation -->
            <nav class="breadcrumb-container" aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="dashboard-admin.php">
                            <img src="assets/img/ico/ico-dashboard.svg" alt="Dashboard" class="breadcrumb-icon">
                            Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="packages.php">
                            <img src="assets/img/ico/ico-packages.svg" alt="Packages" class="breadcrumb-icon">
                            Packages
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <img src="assets/img/ico/ico-packages.svg" alt="Create Package" class="breadcrumb-icon">
                        Create Package
                    </li>
                </ol>
            </nav>

            <!-- Create Package Header -->
            <div class="create-package-header">
                <div class="d-flex align-items-center justify-content-between">
                    <h3 class="section-title mb-0">
                        <img src="assets/img/ico/ico-packages.svg" alt="Create Package" class="section-icon">
                        Create New Package
                    </h3>
                    <a href="packages.php" class="btn btn-add-package">
                        <i class="bi bi-arrow-left"></i> Back to Packages
                    </a>
                </div>
            </div>

            <!-- Create Package Form -->
            <div class="create-package-container">
                <form id="createPackageForm" class="package-form">
                    
                    <!-- Package Basic Information -->
                    <div class="form-section">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="packageName" class="form-label">Package Name</label>
                                    <input type="text" class="form-control" id="packageName" name="packageName" placeholder="Enter package name" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="regularPrice" class="form-label">Regular Price</label>
                                    <input type="number" class="form-control" id="regularPrice" name="regularPrice" placeholder="Enter regular price" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="discountedPrice" class="form-label">Discounted Price</label>
                                    <input type="number" class="form-control" id="discountedPrice" name="discountedPrice" placeholder="Enter discounted price" step="0.01">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="toursType" class="form-label">Tours Type</label>
                                    <select class="form-control admin-select" id="toursType" name="toursType" required>
                                        <option value="">Select Tours Type</option>
                                        <option value="day-trip">Day Trip</option>
                                        <option value="multi-day-tours">Multi-Day Tours</option>
                                        <option value="holiday-tours">Holiday Tours</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="destinations" class="form-label">Select Destinations</label>
                                    <select class="form-control admin-select" id="destinations" name="destinations" required>
                                        <option value="">Select Destination</option>
                                        <option value="barisal">Barisal</option>
                                        <option value="chittagong">Chittagong</option>
                                        <option value="dhaka">Dhaka</option>
                                        <option value="mymensingh">Mymensingh</option>
                                        <option value="rajshahi">Rajshahi</option>
                                        <option value="rangpur">Rangpur</option>
                                        <option value="sylhet">Sylhet</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="makeFeatured" name="makeFeatured">
                                    <label class="form-check-label" for="makeFeatured">
                                        Make this Featured?
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="showOnWebsite" name="showOnWebsite" checked>
                                    <label class="form-check-label" for="showOnWebsite">
                                        Show this on Website?
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="popularDayTrips" name="popularDayTrips">
                                    <label class="form-check-label" for="popularDayTrips">
                                        Popular Day Trips?
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="travellersChoice" name="travellersChoice">
                                    <label class="form-check-label" for="travellersChoice">
                                        Tours by Travellers Choice?
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Activities Selection -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label class="form-label">Select Activities</label>
                                    <div class="activities-checkboxes">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check activity-checkbox">
                                                    <input class="form-check-input" type="checkbox" id="artCrafts" name="activities[]" value="art-crafts">
                                                    <label class="form-check-label" for="artCrafts">
                                                        Art & Crafts
                                                    </label>
                                                </div>
                                                <div class="form-check activity-checkbox">
                                                    <input class="form-check-input" type="checkbox" id="boatCruise" name="activities[]" value="boat-cruise">
                                                    <label class="form-check-label" for="boatCruise">
                                                        Boat & Cruise
                                                    </label>
                                                </div>
                                                <div class="form-check activity-checkbox">
                                                    <input class="form-check-input" type="checkbox" id="culinaryFood" name="activities[]" value="culinary-food">
                                                    <label class="form-check-label" for="culinaryFood">
                                                        Culinary & Food
                                                    </label>
                                                </div>
                                                <div class="form-check activity-checkbox">
                                                    <input class="form-check-input" type="checkbox" id="cyclingCamping" name="activities[]" value="cycling-camping">
                                                    <label class="form-check-label" for="cyclingCamping">
                                                        Cycling & Camping
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check activity-checkbox">
                                                    <input class="form-check-input" type="checkbox" id="folkFestivals" name="activities[]" value="folk-festivals">
                                                    <label class="form-check-label" for="folkFestivals">
                                                        Folk & Festivals
                                                    </label>
                                                </div>
                                                <div class="form-check activity-checkbox">
                                                    <input class="form-check-input" type="checkbox" id="historyHeritage" name="activities[]" value="history-heritage">
                                                    <label class="form-check-label" for="historyHeritage">
                                                        History & Heritage
                                                    </label>
                                                </div>
                                                <div class="form-check activity-checkbox">
                                                    <input class="form-check-input" type="checkbox" id="natureWildlife" name="activities[]" value="nature-wildlife">
                                                    <label class="form-check-label" for="natureWildlife">
                                                        Nature & Wildlife
                                                    </label>
                                                </div>
                                                <div class="form-check activity-checkbox">
                                                    <input class="form-check-input" type="checkbox" id="trailingWalking" name="activities[]" value="trailing-walking">
                                                    <label class="form-check-label" for="trailingWalking">
                                                        Trailing & Walking
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activities Logs Section -->
                    <div class="form-section">
                        <h4 class="section-subtitle">Add Activities Logs:</h4>
                        
                        <!-- Day Navigation -->
                        <div class="day-navigation mb-4">
                            <div class="day-tabs">
                                <button type="button" class="day-tab active" data-day="1">
                                    <span class="day-number">Day 1</span>
                                </button>
                                <div class="day-arrow">→</div>
                                <button type="button" class="day-tab" data-day="2">
                                    <span class="day-number">Day 2</span>
                                </button>
                                <div class="day-arrow">→</div>
                                <button type="button" class="day-tab" data-day="3">
                                    <span class="day-number">Day 3</span>
                                </button>
                                <div class="day-arrow">→</div>
                                <button type="button" class="day-tab" data-day="4">
                                    <span class="day-number">Day 4</span>
                                </button>
                            </div>
                        </div>

                        <!-- Day 1 Activities -->
                        <div class="day-content active" id="day-1">
                            <h5 class="day-title">Day 1</h5>
                            <div class="activities-list">
                                <div class="form-check activity-item">
                                    <input class="form-check-input" type="checkbox" id="day1_pickup" name="day1_activities[]" value="pickup">
                                    <label class="form-check-label" for="day1_pickup">
                                        Pick Up from the Airport
                                    </label>
                                </div>
                                <div class="form-check activity-item">
                                    <input class="form-check-input" type="checkbox" id="day1_hotel" name="day1_activities[]" value="hotel">
                                    <label class="form-check-label" for="day1_hotel">
                                        Drop off to Hotel
                                    </label>
                                </div>
                                <div class="form-check activity-item">
                                    <input class="form-check-input" type="checkbox" id="day1_lunch" name="day1_activities[]" value="lunch">
                                    <label class="form-check-label" for="day1_lunch">
                                        Lunch
                                    </label>
                                </div>
                                <div class="form-check activity-item">
                                    <input class="form-check-input" type="checkbox" id="day1_siteseeing" name="day1_activities[]" value="siteseeing">
                                    <label class="form-check-label" for="day1_siteseeing">
                                        Site seeing
                                    </label>
                                </div>
                                <div class="form-check activity-item">
                                    <input class="form-check-input" type="checkbox" id="day1_dinner" name="day1_activities[]" value="dinner">
                                    <label class="form-check-label" for="day1_dinner">
                                        Dinner
                                    </label>
                                </div>
                                <div class="form-check activity-item">
                                    <input class="form-check-input" type="checkbox" id="day1_dropoff" name="day1_activities[]" value="dropoff">
                                    <label class="form-check-label" for="day1_dropoff">
                                        Drop off to Hotel
                                    </label>
                                </div>
                            </div>
                            <button type="button" class="btn btn-add-activity">
                                <i class="bi bi-plus-circle"></i> Add more
                            </button>
                        </div>

                        <!-- Day 2 Activities (Hidden by default) -->
                        <div class="day-content" id="day-2" style="display: none;">
                            <h5 class="day-title">Day 2</h5>
                            <div class="activities-list">
                                <!-- Activities will be added dynamically -->
                            </div>
                            <button type="button" class="btn btn-add-activity">
                                <i class="bi bi-plus-circle"></i> Add more
                            </button>
                        </div>

                        <!-- Day 3 Activities (Hidden by default) -->
                        <div class="day-content" id="day-3" style="display: none;">
                            <h5 class="day-title">Day 3</h5>
                            <div class="activities-list">
                                <!-- Activities will be added dynamically -->
                            </div>
                            <button type="button" class="btn btn-add-activity">
                                <i class="bi bi-plus-circle"></i> Add more
                            </button>
                        </div>

                        <!-- Day 4 Activities (Hidden by default) -->
                        <div class="day-content" id="day-4" style="display: none;">
                            <h5 class="day-title">Day 4</h5>
                            <div class="activities-list">
                                <!-- Activities will be added dynamically -->
                            </div>
                            <button type="button" class="btn btn-add-activity">
                                <i class="bi bi-plus-circle"></i> Add more
                            </button>
                        </div>
                    </div>

                    <!-- Package Details Section -->
                    <div class="form-section">
                        <h4 class="section-subtitle">Package Details:</h4>

                        <!-- Tour Information Grid -->
                        <div class="tour-info-grid">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="tourType" class="form-label">
                                            <i class="bi bi-calendar-check text-success me-2"></i>Tour Type
                                        </label>
                                        <input type="text" class="form-control" id="tourType" name="tourType" placeholder="e.g., Day Tour" value="Day Tour">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="duration" class="form-label">
                                            <i class="bi bi-clock text-success me-2"></i>Duration
                                        </label>
                                        <input type="text" class="form-control" id="duration" name="duration" placeholder="e.g., 10 Hours (+/-)">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="groupSize" class="form-label">
                                            <i class="bi bi-people text-success me-2"></i>Group Size
                                        </label>
                                        <input type="text" class="form-control" id="groupSize" name="groupSize" placeholder="e.g., 8 Persons (Max)">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="accommodation" class="form-label">
                                            <i class="bi bi-building text-success me-2"></i>Accommodation
                                        </label>
                                        <input type="text" class="form-control" id="accommodation" name="accommodation" placeholder="e.g., 0 (Not Included)">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="transportation" class="form-label">
                                            <i class="bi bi-car-front text-success me-2"></i>Transportation
                                        </label>
                                        <input type="text" class="form-control" id="transportation" name="transportation" placeholder="e.g., A/C Vehicle">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="meals" class="form-label">
                                            <i class="bi bi-cup-straw text-success me-2"></i>Meals
                                        </label>
                                        <input type="text" class="form-control" id="meals" name="meals" placeholder="e.g., 1 (Lunch/person)">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="fitnessLevel" class="form-label">
                                            <i class="bi bi-activity text-success me-2"></i>Fitness Level
                                        </label>
                                        <input type="text" class="form-control" id="fitnessLevel" name="fitnessLevel" placeholder="e.g., Moderate">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="minimumAge" class="form-label">
                                            <i class="bi bi-person text-success me-2"></i>Minimum Age
                                        </label>
                                        <input type="text" class="form-control" id="minimumAge" name="minimumAge" placeholder="e.g., 6">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="language" class="form-label">
                                            <i class="bi bi-translate text-success me-2"></i>Language
                                        </label>
                                        <input type="text" class="form-control" id="language" name="language" placeholder="e.g., English" value="English">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="bestSeason" class="form-label">
                                            <i class="bi bi-sun text-success me-2"></i>Best Season
                                        </label>
                                        <input type="text" class="form-control" id="bestSeason" name="bestSeason" placeholder="e.g., All Year Round">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="guidingMethod" class="form-label">
                                            <i class="bi bi-person-check text-success me-2"></i>Guiding Method
                                        </label>
                                        <input type="text" class="form-control" id="guidingMethod" name="guidingMethod" placeholder="e.g., Full Time">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label for="location" class="form-label">
                                            <i class="bi bi-geo-alt text-success me-2"></i>Location
                                        </label>
                                        <input type="text" class="form-control" id="location" name="location" placeholder="e.g., New & Old Dhaka">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="maxAltitude" class="form-label">
                                            <i class="bi bi-mountain text-success me-2"></i>Max Altitude
                                        </label>
                                        <input type="text" class="form-control" id="maxAltitude" name="maxAltitude" placeholder="e.g., Not Applicable">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="arrivalOn" class="form-label">
                                            <i class="bi bi-airplane-engines text-success me-2"></i>Arrival On
                                        </label>
                                        <input type="text" class="form-control" id="arrivalOn" name="arrivalOn" placeholder="e.g., Dhaka, Bangladesh">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-3">
                                        <label for="departureFrom" class="form-label">
                                            <i class="bi bi-airplane text-success me-2"></i>Departure From
                                        </label>
                                        <input type="text" class="form-control" id="departureFrom" name="departureFrom" placeholder="e.g., Dhaka, Bangladesh">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Subtitle -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="packageSubtitle" class="form-label">Subtitle</label>
                                    <input type="text" class="form-control" id="packageSubtitle" name="packageSubtitle" placeholder="Enter package subtitle">
                                </div>
                            </div>
                        </div>

                        <!-- Intro -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="packageIntro" class="form-label">Intro</label>
                                    <textarea class="form-control" id="packageIntro" name="packageIntro" rows="4" placeholder="Enter package introduction"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="tourHighlights" class="form-label">Tour Highlights</label>
                                    <textarea class="form-control" id="tourHighlights" name="tourHighlights" rows="4" placeholder="Enter tour highlights"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="tourIncludes" class="form-label">Tour Includes</label>
                                    <textarea class="form-control" id="tourIncludes" name="tourIncludes" rows="4" placeholder="Enter what the tour includes"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="doesNotInclude" class="form-label">Does not Includes</label>
                                    <textarea class="form-control" id="doesNotInclude" name="doesNotInclude" rows="4" placeholder="Enter what the tour does not include"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-section">
                        <div class="d-flex justify-content-center">
                            <button type="submit" class="btn btn-create-package">
                                Create Package
                            </button>
                        </div>
                    </div>

                </form>
            </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Day tab functionality
    const dayTabs = document.querySelectorAll('.day-tab');
    const dayContents = document.querySelectorAll('.day-content');

    dayTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const dayNumber = this.getAttribute('data-day');

            // Remove active class from all tabs and contents
            dayTabs.forEach(t => t.classList.remove('active'));
            dayContents.forEach(c => {
                c.classList.remove('active');
                c.style.display = 'none';
            });

            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            const targetContent = document.getElementById(`day-${dayNumber}`);
            if (targetContent) {
                targetContent.classList.add('active');
                targetContent.style.display = 'block';
            }
        });
    });

    // Add more activities functionality
    const addActivityButtons = document.querySelectorAll('.btn-add-activity');

    addActivityButtons.forEach(button => {
        button.addEventListener('click', function() {
            const dayContent = this.closest('.day-content');
            const activitiesList = dayContent.querySelector('.activities-list');
            const dayId = dayContent.id;
            const dayNumber = dayId.split('-')[1];
            const activityCount = activitiesList.querySelectorAll('.activity-item').length;

            // Create new activity item
            const newActivity = document.createElement('div');
            newActivity.className = 'form-check activity-item';
            newActivity.innerHTML = `
                <input class="form-check-input" type="checkbox" id="${dayId}_custom${activityCount}" name="day${dayNumber}_activities[]" value="custom${activityCount}">
                <label class="form-check-label" for="${dayId}_custom${activityCount}">
                    <input type="text" class="form-control activity-input" placeholder="Enter activity name" onchange="updateActivityValue(this)">
                </label>
                <button type="button" class="btn btn-remove-activity" onclick="removeActivity(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;

            activitiesList.appendChild(newActivity);
        });
    });
});

function updateActivityValue(input) {
    const checkbox = input.closest('.activity-item').querySelector('input[type="checkbox"]');
    checkbox.value = input.value;
}

function removeActivity(button) {
    button.closest('.activity-item').remove();
}

// Form submission
document.getElementById('createPackageForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Collect form data
    const formData = new FormData(this);

    // Here you would typically send the data to a server
    console.log('Package data:', Object.fromEntries(formData));

    // Show success message (you can replace this with actual form submission)
    alert('Package created successfully!');
});
</script>

<?php include 'inc/footer.php'; ?>
