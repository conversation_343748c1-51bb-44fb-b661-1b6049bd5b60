<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bangladesh Unbound - Login</title>
    <!-- Google Fonts - Poppins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap 5.3.2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/custom.css" rel="stylesheet">
</head>
<body class="login-body">
    <div class="login-container">
        <!-- Left Side - Login Form -->
        <div class="login-form-section">
            <div class="login-form-container">
                <!-- Logo -->
                <div class="login-logo">
                    <img src="assets/img/logo.png" alt="Bangladesh Unbound" class="login-logo-img">
                </div>

                <!-- Welcome Text -->
                <div class="login-welcome">
                    <h1>Welcome Back</h1>
                    <p>Sign in to your account to continue your journey</p>
                </div>

                <!-- Login Form -->
                <form class="login-form" action="dashboard.php" method="POST">
                    <div class="form-group">
                        <label for="email" class="form-label">
                            <img src="assets/img/ico/ico-profile.svg" alt="Email" class="form-icon">
                            Email Address
                        </label>
                        <input type="email" class="form-control login-input" id="email" name="email" placeholder="Enter your email" required>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">
                            <img src="assets/img/ico/ico-logout.svg" alt="Password" class="form-icon">
                            Password
                        </label>
                        <div class="password-input-container">
                            <input type="password" class="form-control login-input" id="password" name="password" placeholder="Enter your password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <img src="assets/img/ico/ico-status.svg" alt="Show" class="password-toggle-icon" id="passwordToggleIcon">
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <div class="remember-me">
                            <input type="checkbox" id="remember" name="remember" class="form-check-input">
                            <label for="remember" class="form-check-label">Remember me</label>
                        </div>
                        <a href="#" class="forgot-password">Forgot Password?</a>
                    </div>

                    <button type="submit" class="btn btn-login">
                        <img src="assets/img/ico/ico-dashboard.svg" alt="Login" class="btn-icon">
                        Sign In
                    </button>

                    <div class="login-divider">
                        <span>or</span>
                    </div>

                    <!-- <div class="social-login">
                        <button type="button" class="btn btn-social btn-google">
                            <img src="assets/img/ico/ico-status.svg" alt="Google" class="social-icon">
                            Continue with Google
                        </button>
                        <button type="button" class="btn btn-social btn-facebook">
                            <img src="assets/img/ico/ico-bookings.svg" alt="Facebook" class="social-icon">
                            Continue with Facebook
                        </button>
                    </div> -->

                    <div class="signup-link">
                        <p>Don't have an account? <a href="#">Sign up here</a></p>
                    </div>
                </form>
            </div>
        </div>

        <!-- Right Side - Hero Image -->
        <div class="login-hero-section">
            <div class="hero-content">
                <div class="hero-overlay">
                    <h2>Discover Bangladesh</h2>
                    <p>Experience the beauty, culture, and adventure that Bangladesh has to offer with our expertly crafted tours.</p>
                    <div class="hero-features">
                        <div class="feature-item">
                            <img src="assets/img/ico/ico-package.svg" alt="Tours" class="feature-icon">
                            <span>Expert Guided Tours</span>
                        </div>
                        <div class="feature-item">
                            <img src="assets/img/ico/ico-status.svg" alt="Safe" class="feature-icon">
                            <span>Safe & Secure Travel</span>
                        </div>
                        <div class="feature-item">
                            <img src="assets/img/ico/ico-custom.svg" alt="Custom" class="feature-icon">
                            <span>Customized Experiences</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.style.opacity = '0.7';
            } else {
                passwordInput.type = 'password';
                toggleIcon.style.opacity = '1';
            }
        }

        // Form validation and animation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.login-form');
            const inputs = document.querySelectorAll('.login-input');

            // Add focus animations
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (this.value === '') {
                        this.parentElement.classList.remove('focused');
                    }
                });

                // Check if input has value on load
                if (input.value !== '') {
                    input.parentElement.classList.add('focused');
                }
            });

            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Add loading state
                const submitBtn = document.querySelector('.btn-login');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Signing In...';
                submitBtn.disabled = true;

                // Simulate login process
                setTimeout(() => {
                    window.location.href = 'dashboard.php';
                }, 1500);
            });
        });
    </script>
</body>
</html>
